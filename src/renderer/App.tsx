import React, { useState, useEffect } from 'react';
import './App.css';
import TranscriptPanel from './components/TranscriptPanel';
import QuestionsPanel from './components/QuestionsPanel';
import AnswerPanel from './components/AnswerPanel';
import SettingsPanel from './components/SettingsPanel';
import Footer from './components/Footer';

// Define the ElectronAPI interface to match the preload script
interface ElectronAPI {
  getTranscript: () => Promise<string>;
  clearTranscript: () => Promise<void>;
  startTranscription: () => Promise<{ success: boolean }>;
  stopTranscription: () => Promise<{ success: boolean }>;
  isRecording: () => Promise<{ isRecording: boolean }>;
  onTranscriptUpdate: (callback: (data: { text: string, isPartial: boolean }) => void) => () => void;
  onTranscriptCleared: (callback: () => void) => () => void;
  extractQuestions: () => Promise<string[]>;
  onQuestionsExtracted: (callback: (questions: string[]) => void) => () => void;
  generateAnswer: (question: string) => Promise<string>;
  onGenerateAnswerRequest: (callback: () => void) => () => void;
  getUserSettings: () => Promise<{
    resume: string;
    jobDescription: string;
    additionalContext: string;
  }>;
  saveUserSettings: (settings: {
    resume: string;
    jobDescription: string;
    additionalContext: string;
  }) => Promise<void>;
  toggleWindow: () => Promise<void>;
  onToggleWindow: (callback: () => void) => () => void;
  onSetView: (callback: (view: string) => void) => () => void;
  onRecordingStatusChanged: (callback: (data: { isRecording: boolean }) => void) => () => void;

  // API key management
  getApiKeys: () => Promise<{ openai: string, assemblyai: string }>;
  saveApiKeys: (keys: { openai: string, assemblyai: string }) => Promise<{ success: boolean }>;
  testOpenAIConnection: (apiKey?: string) => Promise<{ success: boolean }>;
  testAssemblyAIConnection: (apiKey?: string) => Promise<{ success: boolean }>;
  clearApiKeys: () => Promise<{ success: boolean }>;
}

// Declare the window object with the electronAPI property
declare global {
  interface Window {
    electronAPI: ElectronAPI;
  }
}

function App() {
  const [transcript, setTranscript] = useState<string>('');
  const [partialTranscript, setPartialTranscript] = useState<string>('');
  const [questions, setQuestions] = useState<string[]>([]);
  const [selectedQuestion, setSelectedQuestion] = useState<string>('');
  const [answer, setAnswer] = useState<string>('');
  const [isGeneratingAnswer, setIsGeneratingAnswer] = useState<boolean>(false);
  const [isSettingsOpen, setIsSettingsOpen] = useState<boolean>(false);
  const [isRecording, setIsRecording] = useState<boolean>(true); // Default to true since recording starts automatically
  const [isAnswerExpanded, setIsAnswerExpanded] = useState<boolean>(true); // Default to expanded view
  const [settings, setSettings] = useState({
    resume: '',
    jobDescription: '',
    additionalContext: ''
  });

  // Load initial transcript and settings
  useEffect(() => {
    const loadInitialData = async () => {
      try {
        const savedTranscript = await window.electronAPI.getTranscript();
        setTranscript(savedTranscript);

        const savedSettings = await window.electronAPI.getUserSettings();
        setSettings(savedSettings);

        // Check if recording is active
        const recordingStatus = await window.electronAPI.isRecording();
        setIsRecording(recordingStatus.isRecording);
      } catch (error) {
        console.error('Error loading initial data:', error);
      }
    };

    loadInitialData();
  }, []);

  // Set up event listeners
  useEffect(() => {
    // Use a debounce mechanism for transcript updates to reduce state updates
    let partialTranscriptTimeout: NodeJS.Timeout | null = null;

    const removeTranscriptListener = window.electronAPI.onTranscriptUpdate((data) => {
      if (data.isPartial) {
        // For partial updates, use a debounce to reduce state updates
        if (partialTranscriptTimeout) {
          clearTimeout(partialTranscriptTimeout);
        }

        partialTranscriptTimeout = setTimeout(() => {
          setPartialTranscript(data.text);
        }, 33); // ~30fps update rate for smoother transcript rendering
      } else {
        // For final transcript updates, clear any pending partial updates
        if (partialTranscriptTimeout) {
          clearTimeout(partialTranscriptTimeout);
          partialTranscriptTimeout = null;
        }

        setTranscript(data.text);
        setPartialTranscript('');
      }
    });

    const removeTranscriptClearedListener = window.electronAPI.onTranscriptCleared(() => {
      setTranscript('');
      setPartialTranscript('');
    });

    const removeQuestionsExtractedListener = window.electronAPI.onQuestionsExtracted((extractedQuestions) => {
      setQuestions(extractedQuestions);
      if (extractedQuestions.length > 0) {
        setSelectedQuestion(extractedQuestions[0]);
      }
    });

    const removeGenerateAnswerRequestListener = window.electronAPI.onGenerateAnswerRequest(() => {
      if (selectedQuestion) {
        handleGenerateAnswer();
      }
    });

    const removeToggleWindowListener = window.electronAPI.onToggleWindow(() => {
      // This is handled by the main process, but we might want to do something in the UI
    });

    // Listen for recording status changes
    const removeRecordingStatusListener = window.electronAPI.onRecordingStatusChanged((data) => {
      setIsRecording(data.isRecording);
    });

    // Clean up event listeners
    return () => {
      removeTranscriptListener();
      removeTranscriptClearedListener();
      removeQuestionsExtractedListener();
      removeGenerateAnswerRequestListener();
      removeToggleWindowListener();
      removeRecordingStatusListener();
    };
  }, [selectedQuestion]);

  const handleExtractQuestions = async () => {
    try {
      const extractedQuestions = await window.electronAPI.extractQuestions();
      setQuestions(extractedQuestions);
      if (extractedQuestions.length > 0) {
        setSelectedQuestion(extractedQuestions[0]);
      }
    } catch (error) {
      console.error('Error extracting questions:', error);
    }
  };

  const handleGenerateAnswer = async () => {
    if (!selectedQuestion || isGeneratingAnswer) return;

    setIsGeneratingAnswer(true);
    setAnswer('Generating answer...');

    try {
      const generatedAnswer = await window.electronAPI.generateAnswer(selectedQuestion);
      setAnswer(generatedAnswer);
    } catch (error) {
      console.error('Error generating answer:', error);
      setAnswer('Error generating answer. Please try again.');
    } finally {
      setIsGeneratingAnswer(false);
    }
  };

  const handleClearTranscript = async () => {
    try {
      await window.electronAPI.clearTranscript();
      setQuestions([]);
      setSelectedQuestion('');
      setAnswer('');
    } catch (error) {
      console.error('Error clearing transcript:', error);
    }
  };

  const handleStopRecording = async () => {
    try {
      await window.electronAPI.stopTranscription();
    } catch (error) {
      console.error('Error stopping recording:', error);
    }
  };

  const handleStartRecording = async () => {
    try {
      await window.electronAPI.startTranscription();
    } catch (error) {
      console.error('Error starting recording:', error);
    }
  };

  const handleSaveSettings = async (newSettings: typeof settings) => {
    try {
      await window.electronAPI.saveUserSettings(newSettings);
      setSettings(newSettings);
      setIsSettingsOpen(false);
    } catch (error) {
      console.error('Error saving settings:', error);
    }
  };

  const toggleAnswerExpanded = () => {
    setIsAnswerExpanded(prev => !prev);
  };

  const toggleSettings = () => {
    setIsSettingsOpen(!isSettingsOpen);
  };

  const handleRestartApp = async () => {
    if (window.confirm('Are you sure you want to restart the app? This will clear all memory and start fresh.')) {
      try {
        await window.electronAPI.restartApp();
      } catch (error) {
        console.error('Error restarting app:', error);
      }
    }
  };

  return (
    <div className="app">
      <header className="app-header">
        <h1>Interview Assistant</h1>
        <div className="header-buttons">
          <div className="recording-controls">
            {isRecording ? (
              <button
                onClick={handleStopRecording}
                className="stop-recording-button"
                title="Stop Recording"
              >
                ⏹️ Stop Recording
              </button>
            ) : (
              <button
                onClick={handleStartRecording}
                className="start-recording-button"
                title="Start Recording"
              >
                🎙️ Start Recording
              </button>
            )}
          </div>
          <button onClick={handleExtractQuestions} title="Extract Questions (Cmd+H)">
            Extract Questions
          </button>
          <button onClick={handleClearTranscript} title="Clear Transcript (Cmd+R)">
            Clear Transcript
          </button>
          <button onClick={toggleAnswerExpanded} className="expand-button" title="Toggle Answer Panel Size">
            {isAnswerExpanded ? 'Standard View' : 'Expanded View'}
          </button>
          <button onClick={toggleSettings} className="settings-button">
            ⚙️ Settings
          </button>
          <button onClick={handleRestartApp} className="restart-button" title="Restart App (Clear Memory)">
            🔄
          </button>
        </div>
      </header>

      <div className={`app-content ${isAnswerExpanded ? 'answer-expanded' : ''}`}>
        <div className="left-panel">
          <TranscriptPanel
            transcript={transcript}
            partialTranscript={partialTranscript}
            isRecording={isRecording}
          />
        </div>
        <div className="right-panel">
          <QuestionsPanel
            questions={questions}
            selectedQuestion={selectedQuestion}
            onSelectQuestion={setSelectedQuestion}
            onGenerateAnswer={handleGenerateAnswer}
          />
          <AnswerPanel
            question={selectedQuestion}
            answer={answer}
            isGenerating={isGeneratingAnswer}
          />
        </div>
      </div>

      <Footer />

      {isSettingsOpen && (
        <SettingsPanel
          settings={settings}
          onSave={handleSaveSettings}
          onClose={() => setIsSettingsOpen(false)}
        />
      )}
    </div>
  );
}

export default App;
